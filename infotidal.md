# Tidal API Documentation

## Overview

This document contains comprehensive information about the Tidal API integration used in the Glass Player music application. The API provides access to Tidal's music streaming service with high-quality audio formats including FLAC.

## API Base URLs

The implementation uses multiple fallback endpoints for reliability:

```javascript
const TIDAL_FALLBACK_ENDPOINTS = [
  "https://tidal.401658.xyz",
  "https://api.tidal.401658.xyz",
  "https://tidal-api.vercel.app",
];
```

**Primary Documentation**: https://tidal.401658.xyz/tdoc

## Authentication

The API appears to be **publicly accessible** without requiring authentication tokens or API keys. This makes it suitable for client-side applications.

## Core Endpoints

### 1. Search Tracks

**Endpoint**: `/api/tidal/search`
**Method**: GET
**Parameters**:

- `q` (string): Search query (song title, artist, etc.)
- `limit` (number, optional): Number of results (default: 25)
- `offset` (number, optional): Pagination offset (default: 0)

**Example Request**:

```
GET /api/tidal/search?q=That's So True Gracie Abrams&limit=25&offset=0
```

**Response Structure**:

```json
{
  "limit": 25,
  "offset": 0,
  "totalNumberOfItems": 1,
  "items": [
    {
      "id": 393536993,
      "title": "That's So True",
      "duration": 166,
      "replayGain": -9.87,
      "peak": 0.999999,
      "allowStreaming": true,
      "streamReady": true,
      "artist": {
        "id": 3523103,
        "name": "Gracie Abrams",
        "type": "MAIN"
      },
      "album": {
        "id": 393536992,
        "title": "The Secret of Us (Deluxe)",
        "cover": "f49a74ea-9853-415b-b22f-2eb8baf4e1ab"
      }
    }
  ]
}
```

### 2. Get Track by ID

**Endpoint**: `/api/tidal/track/{trackId}`
**Method**: GET
**Parameters**:

- `trackId` (number): Tidal track ID
- `quality` (string): Audio quality (LOW, HIGH, LOSSLESS, HI_RES, HI_RES_LOSSLESS)

**Example Request**:

```
GET /api/tidal/track/393536993?quality=LOSSLESS
```

**Response Structure** (Array format):

```json
[
  {
    "id": 430965666,
    "title": "That's So True",
    "duration": 161,
    "replayGain": -10.52,
    "peak": 1.135118,
    "allowStreaming": true,
    "streamReady": true,
    "artist": {
      "id": 3523103,
      "name": "Our Last Night",
      "type": "MAIN",
      "picture": "0ece0774-1bbb-4a08-9435-80a021692eb9"
    },
    "album": {
      "id": 430965665,
      "title": "That's So True",
      "cover": "f49a74ea-9853-415b-b22f-2eb8baf4e1ab"
    }
  },
  {
    "trackId": 430965666,
    "assetPresentation": "FULL",
    "audioMode": "STEREO",
    "audioQuality": "LOSSLESS",
    "manifestMimeType": "application/vnd.tidal.bts",
    "bitDepth": 16,
    "sampleRate": 44100
  },
  {
    "OriginalTrackUrl": "https://sp-pr-cf.audio.tidal.com/mediatracks/CAEaKAgDEiQxODhjOWE4ZTMxNWY4MWNjMWM3ZjZjMDMyOGUzZTI5ZC5tcDQ/0.flac?Expires=**********&Signature=..."
  }
]
```

### 3. Get Lyrics

**Endpoint**: `/api/tidal/lyrics/{trackId}`
**Method**: GET

**Response Structure**:

```json
{
  "trackId": 393536993,
  "lyricsProvider": "LyricFind",
  "lyrics": "Plain text lyrics...",
  "subtitles": "[00:00.00] Lyric line 1\n[00:05.00] Lyric line 2...",
  "isRightToLeft": false
}
```

## Audio Quality Levels

| Quality           | Format | Bitrate/Quality | Description           |
| ----------------- | ------ | --------------- | --------------------- |
| `LOW`             | AAC    | 96 kbps         | Basic quality         |
| `HIGH`            | AAC    | 320 kbps        | High quality lossy    |
| `LOSSLESS`        | FLAC   | 16-bit/44.1kHz  | CD quality lossless   |
| `HI_RES`          | FLAC   | 24-bit/96kHz    | High resolution       |
| `HI_RES_LOSSLESS` | FLAC   | 24-bit/192kHz   | Ultra high resolution |

## Browser Compatibility

### FLAC Support

- ✅ **Firefox**: Native FLAC support
- ❌ **Chrome/Chromium**: No native FLAC support
- ❌ **Safari**: Limited FLAC support
- ❌ **Edge**: No native FLAC support

### Recommended Qualities by Browser

- **Chrome/Edge/Chromium**: Use `HIGH` (AAC 320kbps)
- **Firefox**: Can use `LOSSLESS` or higher (FLAC)
- **Safari**: Use `HIGH` (AAC 320kbps)

## Implementation Details

### Search Flow

1. **Search for tracks** using `/api/tidal/search`
2. **Extract track ID** from search results
3. **Get streaming URL** using `/api/tidal/track/{id}`
4. **Parse response array** to extract `OriginalTrackUrl`

### Response Parsing

The `/track/` endpoint returns an array with three objects:

1. **Index 0**: Track metadata (title, artist, album, etc.)
2. **Index 1**: Technical audio info (quality, sample rate, etc.)
3. **Index 2**: Streaming URL object with `OriginalTrackUrl`

### URL Structure

Streaming URLs follow this pattern:

```
https://sp-pr-cf.audio.tidal.com/mediatracks/{encoded_path}/{file}.{format}?Expires={timestamp}&Signature={signature}&Key-Pair-Id={key_id}
```

- **Signed URLs** with expiration timestamps
- **Format**: `.flac` for lossless, `.mp4` for AAC
- **CDN**: Cloudfront distribution (`sp-pr-cf.audio.tidal.com`)

## Error Handling

### Common Issues

1. **Track not found**: Returns empty `items` array
2. **Quality not available**: Falls back to available quality
3. **Expired URLs**: URLs have limited lifetime (~24 hours)
4. **Rate limiting**: Multiple endpoints provide redundancy

### Fallback Strategy

```javascript
// Multiple API endpoints for reliability
const endpoints = [
  "https://tidal.401658.xyz",
  "https://api.tidal.401658.xyz",
  "https://tidal-api.vercel.app",
];

// Automatic endpoint switching on failure
if (error) {
  switchToNextTidalEndpoint();
  retry();
}
```

## Integration Examples

### Basic Search

```javascript
const searchResults = await fetch(
  `${apiBase}/api/tidal/search?q=${encodeURIComponent(query)}&limit=25`
);
const data = await searchResults.json();
```

### Get Streaming URL

```javascript
const trackResponse = await fetch(
  `${apiBase}/api/tidal/track/${trackId}?quality=LOSSLESS`
);
const [metadata, techInfo, urlObject] = await trackResponse.json();
const streamingUrl = urlObject.OriginalTrackUrl;
```

### Format Detection

```javascript
// Check if browser supports FLAC
const audio = document.createElement("audio");
const supportsFlac = audio.canPlayType("audio/flac").replace(/no/, "");

// Choose appropriate quality
const quality = supportsFlac ? "LOSSLESS" : "HIGH";
```

## Rate Limits & Best Practices

### Recommendations

- **Cache search results** to reduce API calls
- **Implement request timeouts** (15 seconds recommended)
- **Use multiple endpoints** for redundancy
- **Handle expired URLs** gracefully
- **Respect rate limits** (specific limits not documented)

### Performance Tips

- **Preload metadata** before streaming
- **Use appropriate quality** for user's connection
- **Implement progressive loading** for large playlists
- **Cache album artwork** URLs

## Security Considerations

### URL Handling

- **Streaming URLs are temporary** and expire
- **Signatures prevent unauthorized access**
- **No API keys required** for basic access
- **CORS-enabled** for browser usage

### Privacy

- **No user authentication** required
- **Search queries are logged** by the API
- **Streaming generates access logs**
- **Consider user privacy** in implementation

## Limitations

### Known Restrictions

- **No playlist creation/modification**
- **No user account integration**
- **Limited to search and streaming**
- **No offline download capability**
- **URL expiration requires refresh**

### Technical Limits

- **Search limited to 1000 results** (pagination required)
- **Quality depends on track availability**
- **Some tracks may be geo-restricted**
- **API availability not guaranteed**

## Code Examples

### Complete Integration Example

```javascript
// Search and play a track
async function playTidalTrack(query, quality = "LOSSLESS") {
  try {
    // 1. Search for the track
    const searchResponse = await fetch(
      `${TIDAL_API_BASE}/api/tidal/search?q=${encodeURIComponent(query)}`
    );
    const searchData = await searchResponse.json();

    if (!searchData.items || searchData.items.length === 0) {
      throw new Error("No tracks found");
    }

    const track = searchData.items[0];

    // 2. Get streaming URL
    const trackResponse = await fetch(
      `${TIDAL_API_BASE}/api/tidal/track/${track.id}?quality=${quality}`
    );
    const trackData = await trackResponse.json();

    // 3. Extract streaming URL from array response
    const urlObject = trackData.find((item) => item.OriginalTrackUrl);
    if (!urlObject) {
      throw new Error("No streaming URL found");
    }

    // 4. Create audio element and play
    const audio = new Audio(urlObject.OriginalTrackUrl);
    await audio.play();

    return {
      track: trackData[0], // Metadata
      streamingUrl: urlObject.OriginalTrackUrl,
    };
  } catch (error) {
    console.error("Error playing Tidal track:", error);
    throw error;
  }
}
```

### Browser Compatibility Check

```javascript
function getBestTidalQuality() {
  const audio = document.createElement("audio");
  const supportsFlac = audio.canPlayType("audio/flac").replace(/no/, "");

  if (supportsFlac) {
    return "LOSSLESS"; // Firefox, some mobile browsers
  } else {
    return "HIGH"; // Chrome, Safari, Edge
  }
}
```

## Troubleshooting

### Common Error Messages

| Error                               | Cause                 | Solution                            |
| ----------------------------------- | --------------------- | ----------------------------------- |
| `No tracks found`                   | Invalid search query  | Check spelling, try different terms |
| `No streaming URL found`            | Track not available   | Try different quality or track      |
| `Audio error: Format not supported` | FLAC in Chrome        | Switch to HIGH quality (AAC)        |
| `Network error`                     | API endpoint down     | Try fallback endpoints              |
| `Expired URL`                       | Streaming URL expired | Refresh track data                  |

### Debug Tips

```javascript
// Enable detailed logging
console.log("Search results:", searchData);
console.log("Track data array:", trackData);
console.log("Streaming URL:", urlObject.OriginalTrackUrl);
console.log("Audio format:", getAudioFormatFromUrl(url));
```

## API Response Examples

### Successful Search Response

```json
{
  "limit": 25,
  "offset": 0,
  "totalNumberOfItems": 11,
  "items": [
    {
      "id": 393536993,
      "title": "That's So True",
      "duration": 166,
      "replayGain": -9.87,
      "peak": 0.999999,
      "allowStreaming": true,
      "streamReady": true,
      "streamStartDate": "2024-06-21T00:00:00.000+0000",
      "premiumStreamingOnly": false,
      "trackNumber": 12,
      "volumeNumber": 1,
      "popularity": 39,
      "copyright": "2024 Interscope Records",
      "url": "http://www.tidal.com/track/393536993",
      "isrc": "USUM72413110",
      "editable": false,
      "explicit": false,
      "audioQuality": "LOSSLESS",
      "audioModes": ["STEREO"],
      "artist": {
        "id": 4050205,
        "name": "Gracie Abrams",
        "type": "MAIN",
        "picture": "04d63cd8-a1a5-42e0-b1ec-8e336b7d9200"
      },
      "artists": [
        {
          "id": 4050205,
          "name": "Gracie Abrams",
          "type": "MAIN",
          "picture": "04d63cd8-a1a5-42e0-b1ec-8e336b7d9200"
        }
      ],
      "album": {
        "id": 393536992,
        "title": "The Secret of Us (Deluxe)",
        "cover": "f49a74ea-9853-415b-b22f-2eb8baf4e1ab",
        "releaseDate": "2024-10-18"
      }
    }
  ]
}
```

### Track Data Response (Array Format)

```json
[
  {
    "id": 393536993,
    "title": "That's So True",
    "duration": 166,
    "replayGain": -9.87,
    "peak": 0.999999,
    "allowStreaming": true,
    "streamReady": true,
    "artist": { "id": 4050205, "name": "Gracie Abrams" },
    "album": { "id": 393536992, "title": "The Secret of Us (Deluxe)" }
  },
  {
    "trackId": 393536993,
    "assetPresentation": "FULL",
    "audioMode": "STEREO",
    "audioQuality": "LOSSLESS",
    "manifestMimeType": "application/vnd.tidal.bts",
    "bitDepth": 16,
    "sampleRate": 44100,
    "trackReplayGain": -9.87,
    "trackPeakAmplitude": 0.999999
  },
  {
    "OriginalTrackUrl": "https://sp-pr-cf.audio.tidal.com/mediatracks/CAEaKAgDEiQxODhjOWE4ZTMxNWY4MWNjMWM3ZjZjMDMyOGUzZTI5ZC5tcDQ/0.flac?Expires=**********&Signature=dwA-kSzDPrjz7l8BIfvNCdDqVXozk9I0oHORgyqgZZWObcjSoetlvLZqfbErX0KgBW9wE4OCyOa8a4OSNbsJJSiE-cjkXNZrERljuC~0gN-XNphbp~mC8nTqkx3BP~fUdKrlT9Naffg-0WqMY8aZsyEY2FrLH9A~6Z5vgFpeBn24uhgm5FmO1-rFgJp99thYCfTSQil3sjpgnXEgDSUx4kOnKcX9RdC-eGdIjBuTXnszIikENoCeulxGa8xQyt1biYWpeg9dtF2XpUyOS0HmYYRxBBKqkrJZwQ5WpnYYsNQ3t~T-oonBLgigt3YFT8jeSIhtPtc2rzQ06vxQOH3jpA__&Key-Pair-Id=K14LZCZ9QUI4JL"
  }
]
```

## Future Considerations

### Potential Enhancements

- **Caching layer** for frequently accessed tracks
- **Background URL refresh** before expiration
- **Quality auto-switching** based on connection
- **Offline mode** with local storage
- **User preference persistence**
- **Playlist management** if API expands
- **Social features** integration
- **Analytics and usage tracking**

### API Evolution

- **Official Tidal API** may become available
- **Authentication requirements** might be added
- **Rate limiting** could be implemented
- **New quality formats** (e.g., Dolby Atmos)
- **Enhanced metadata** (lyrics timing, credits)

---

**Last Updated**: January 2025
**API Version**: Undocumented (community-maintained)
**Status**: Active and functional
**Source**: https://github.com/sachinsenal0x64/Hifi-Tui
**Documentation**: https://tidal.401658.xyz/tdoc
