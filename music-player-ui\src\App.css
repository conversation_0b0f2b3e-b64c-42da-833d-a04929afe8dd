/* Modern, clean reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell",
    "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  overflow-x: hidden; /* Allow vertical scrolling but prevent horizontal */
  min-height: 100vh;
  width: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

/* App Layout */
.App {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 20px;
}

/* Background Effects */
.app-background {
  position: fixed;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background-size: cover;
  background-position: center center;
  filter: blur(40px);
  z-index: -2;
  transform: scale(1.05);
}

.background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: -1;
}

/* Main Content Layout */
.content-layout {
  display: flex;
  width: 100%;
  max-width: 1200px;
  min-height: 70vh;
  padding: 20px;
  margin-top: 20px;
  gap: 30px;
  flex-wrap: wrap;
}

/* Player Section */
.player-container {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  backdrop-filter: blur(15px) saturate(180%);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
  padding: 30px;
  display: flex;
  flex-direction: column;
  min-width: 300px;
  position: relative;
  overflow: hidden;
  flex-basis: 400px;
}

/* Lyrics Panel */
.lyrics-panel {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1); /* Match player container */
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  backdrop-filter: blur(15px) saturate(180%);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
  padding: 30px;
  display: flex;
  flex-direction: column;
  height: 500px;
  max-height: 100%;
  overflow: hidden;
  flex-basis: 400px;
}

.lyrics-display {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.lyrics-title {
  font-size: 1.6rem;
  margin-bottom: 25px;
  text-align: center;
  font-weight: 700;
  color: #fff;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
  letter-spacing: 0.02em;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding-bottom: 15px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.lyrics-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 25px; /* Increased padding for better readability */
  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  /* Add padding at the top and bottom for better scrolling experience */
  padding-top: 25px;
  padding-bottom: 45px;
}

.lyrics-container::-webkit-scrollbar {
  width: 4px; /* Thinner scrollbar */
}

.lyrics-container::-webkit-scrollbar-track {
  background: transparent;
}

.lyrics-container::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2); /* More subtle scrollbar */
  border-radius: 2px;
  transition: background-color 0.3s ease;
}

.lyrics-container:hover::-webkit-scrollbar-thumb {
  background-color: rgba(
    255,
    255,
    255,
    0.3
  ); /* Slightly more visible on hover */
}

.lyric-line {
  font-size: 1.2rem;
  line-height: 1.8; /* Comfortable line height for reading */
  margin-bottom: 18px;
  text-align: center;
  color: rgba(
    255,
    255,
    255,
    0.8
  ); /* Slightly reduced opacity for inactive lines */
  transition: all 0.5s ease-in-out;
  padding: 8px 0;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4); /* Subtle shadow for better contrast */
  font-weight: 500;
  position: relative;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.lyric-line.active {
  font-size: 1.4rem;
  font-weight: 700; /* Bolder for active line */
  color: #ffffff; /* Pure white for active line */
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5); /* Stronger shadow for active line */
  transition: all 0.4s ease-in-out;
  position: relative;
  letter-spacing: 0.01em; /* Subtle letter spacing for emphasis */
}

/* Lyrics Toggle Button (primarily for mobile) */
.lyrics-toggle-button {
  margin-top: 20px;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 20px;
  color: #fff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: center;
  display: none; /* Hidden on desktop */
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
}

.lyrics-toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Playlist Container */
.playlist-container {
  width: 100%;
  max-width: 1200px;
  background-color: rgba(
    20,
    20,
    20,
    0.85
  ); /* Much darker for better contrast */
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  backdrop-filter: blur(15px) saturate(180%);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.4);
  padding: 25px;
  margin: 25px 0;
  position: relative;
  overflow: hidden;
}

/* Custom scrollbar for playlist */
.playlist {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  color: white;
}

.playlist::-webkit-scrollbar {
  width: 6px;
}

.playlist::-webkit-scrollbar-track {
  background: transparent;
}

.playlist::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.playlist-title {
  margin-top: 0;
  margin-bottom: 25px;
  font-size: 1.6rem;
  font-weight: 700;
  color: #fff;
  text-align: left;
  letter-spacing: 0.02em;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding-bottom: 15px;
}

.playlist-tracks {
  list-style: none;
  padding: 0;
  margin: 0;
}

.playlist-track {
  display: flex;
  align-items: center;
  padding: 14px 15px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  /* No background, no border-radius, no glass effect */
  background-color: transparent;
  /* Simple separator */
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

/* Strong dark background for entire track row for better contrast */
.playlist-track::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(
    0,
    0,
    0,
    0.6
  ); /* Much darker background for better contrast */
  z-index: -1;
  opacity: 1;
  transition: background-color 0.2s ease;
}

.playlist-track:hover::before {
  background: rgba(0, 0, 0, 0.7); /* Even darker on hover */
}

.playlist-track:hover {
  transform: translateY(-1px);
}

.playlist-track.active {
  border-bottom: none;
}

.playlist-track.active::before {
  background: rgba(255, 58, 94, 0.15); /* Red tint but much more subtle */
  box-shadow: inset 0 0 0 1px rgba(255, 58, 94, 0.3); /* Subtle inner border */
}

.playlist-track-info {
  flex: 1;
  text-align: left;
  margin: 0 12px;
  position: relative;
  z-index: 1;
}

.playlist-track-title {
  font-weight: 700;
  font-size: 1rem;
  margin-bottom: 6px;
  color: #ffffff; /* Solid white for maximum contrast */
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Stronger shadow */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.01em;
}

.playlist-track.active .playlist-track-title {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.playlist-track-artist {
  font-size: 0.9rem; /* Slightly larger */
  color: #ffffff; /* Solid white for maximum contrast */
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Stronger shadow */
  letter-spacing: 0.01em;
}

.playlist-track.active .playlist-track-artist {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.playlist-track-art {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.25);
  flex-shrink: 0; /* Prevent shrinking */
}

.playlist-track-art img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.now-playing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff; /* White for better visibility */
  margin-left: 12px;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
  background-color: rgba(255, 58, 94, 0.9); /* Red background */
  width: 24px;
  height: 24px;
  border-radius: 50%;
  flex-shrink: 0; /* Prevent shrinking */
}

/* Removed pulse animation */

/* Now Playing Display */
.now-playing-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 25px;
}

.album-art-container {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.album-art {
  width: 100%;
  max-width: 250px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.35),
    0 0 0 1px rgba(255, 255, 255, 0.12);
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275),
    box-shadow 0.3s ease, filter 0.3s ease;
  /* Enhanced subtle border */
  border: 1px solid rgba(255, 255, 255, 0.1);
  filter: brightness(1);
}

.album-art:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2);
  filter: brightness(1.05);
}

.track-info-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 10px; /* Reduced margin to decrease space between singer's name and progress bar */
  padding: 0 45px; /* Add padding for volume and heart icons */
}

.track-info {
  text-align: center;
  max-width: 100%;
}

.like-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy animation */
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 5;
}

.like-button:hover {
  color: #ff3a5e;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-50%) scale(1.1);
  filter: brightness(1.1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.heart-icon {
  font-size: 1.3rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy animation */
  /* Improve rendering quality */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  /* Use a thinner stroke for the icon */
  stroke-width: 1.5px; /* Match other icons */
  stroke: currentColor;
  fill: transparent; /* Outline by default */
}

.heart-icon.liked {
  color: #ff3a5e;
  fill: currentColor; /* Fill when liked */
  filter: drop-shadow(0 0 5px rgba(255, 58, 94, 0.5));
  transform: scale(1.15); /* Slightly larger when liked */
  animation: heartPulse 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes heartPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1.15);
  }
}

.track-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.01em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.artist-name {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.01em;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Track Progress */
.track-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  max-width: 100%;
  margin: 15px auto 30px; /* Reduced top margin to decrease space between singer's name and progress bar */
  position: relative;
  padding: 0 5px; /* Increased padding for better alignment */
}

.time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  min-width: 40px;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.02em;
  font-variant-numeric: tabular-nums; /* For better alignment of numbers */
  transition: all 0.2s ease;
  position: relative;
  top: 0; /* Perfect vertical alignment with progress bar */
  display: flex;
  align-items: center;
  height: 16px; /* Match the height of the progress bar thumb */
}

.time.current-time {
  text-align: left;
}

.time.duration {
  text-align: right;
}

.progress-bar:hover + .time,
.progress-bar:hover ~ .time {
  color: rgba(
    255,
    255,
    255,
    0.9
  ); /* Brighten timestamps on progress bar hover */
}

.progress-bar {
  flex-grow: 1;
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 8px; /* Slightly thicker */
  background: rgba(255, 255, 255, 0.15);
  border-radius: 10px; /* Slightly more rounded */
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease-out;
  position: relative;
  overflow: visible;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15); /* Enhanced inner shadow */
}

.progress-bar:hover {
  height: 10px;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  filter: brightness(1.05);
}

.progress-bar::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #fff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  margin-top: -4px;
  transition: all 0.2s ease-out;
  border: 2px solid rgba(255, 255, 255, 0.9);
  /* Enhanced subtle gradient */
  background: linear-gradient(145deg, #ffffff, #f5f5f5);
}

.progress-bar:hover::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  filter: brightness(1.05);
}

.progress-bar:active::-webkit-slider-thumb {
  transform: scale(1.3);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
  transition: all 0.1s ease-out;
}

.progress-bar::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #fff;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  /* Add subtle gradient */
  background: linear-gradient(145deg, #ffffff, #f5f5f5);
}

.progress-bar:hover::-moz-range-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
}

/* Container for playback controls to ensure proper containment */
.playback-controls-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 15px 0 40px; /* Increased bottom margin to increase space between play button and progress bar */
  padding: 0;
  position: relative; /* For positioning context */
}

/* Control cluster as a unified group */
.playback-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative; /* For positioning context */
  padding: 5px 0;
  /* Ensure the entire cluster is perfectly centered */
  margin: 0 auto;
  width: 100%;
}

/* Main controls (prev, play/pause, next) as a centered unit */
.main-controls {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure playback controls stay within container */
.control-button {
  background-color: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy animation */
  /* Enhance icon rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Use thinner stroke for icons */
  stroke-width: 1.5px;
  margin: 0 10px; /* Increased equal spacing between all controls */
}

.control-button:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.control-button:active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.control-button:disabled {
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  background-color: transparent;
}

.play-pause-button {
  width: 70px;
  height: 70px;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85)
  );
  color: #222;
  font-size: 24px;
  position: relative;
  top: -5px; /* Slightly raised */
  transform: translateY(0); /* Reset any transform from parent */
  z-index: 2; /* Ensure button stays on top */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.8); /* Enhanced shadow for depth + inner highlight */
  -webkit-backdrop-filter: blur(5px); /* Safari support */
  backdrop-filter: blur(5px); /* Subtle blur effect */
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy animation */
  margin: 0 10px; /* Match other controls for perfect spacing */
  overflow: hidden; /* For icon transition effect */
}

.play-pause-button:hover {
  background: linear-gradient(145deg, #fff, rgba(255, 255, 255, 0.9));
  color: #000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25),
    inset 0 1px 1px rgba(255, 255, 255, 0.9);
  transform: translateY(-3px) scale(1.02);
}

.play-pause-button:active {
  background: linear-gradient(
    145deg,
    rgba(240, 240, 240, 0.95),
    rgba(250, 250, 250, 0.9)
  );
  color: #000;
  transform: scale(0.98);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15),
    inset 0 1px 1px rgba(255, 255, 255, 0.6);
  transition: all 0.1s ease;
}

/* Volume Control - Integrated with playback controls */
.volume-control {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
  z-index: 5;
}

.volume-control-wrapper {
  position: absolute;
  right: 20px; /* Position at the right side */
  top: 50%;
  transform: translateY(-50%);
}

.volume-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  width: 48px; /* Match other control buttons */
  height: 48px; /* Match other control buttons */
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  /* Enhance icon rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Use thinner stroke for icons */
  stroke-width: 1.5px;
}

.volume-button:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
  filter: brightness(1.1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.volume-button:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
  background-color: rgba(255, 255, 255, 0.15);
}

/* Volume slider popup */
.volume-slider-popup {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.1); /* Match player container */
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  backdrop-filter: blur(15px) saturate(180%);
  border-radius: 15px; /* Match player container */
  border: 1px solid rgba(255, 255, 255, 0.18); /* Match player container */
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25); /* Match player container */
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px; /* Space between popup and button */
  z-index: 10;
  animation: fadeInScale 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

.volume-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 6px; /* Match progress bar thickness */
  height: 120px;
  background: rgba(255, 255, 255, 0.15); /* Match progress bar */
  border-radius: 6px; /* Match progress bar */
  outline: none;
  transition: all 0.3s ease;
  margin: 5px 0;
  cursor: pointer;
}

/* Custom volume slider styles */
.custom-volume-slider {
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  gap: 15px;
}

.custom-volume-track {
  height: 120px;
  width: 6px;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 8px; /* Slightly more rounded */
  position: relative;
  cursor: pointer;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15); /* Match progress bar */
  transition: all 0.2s ease-out;
}

.custom-volume-track:hover {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.custom-volume-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px; /* Match track */
  transition: height 0.2s ease-out, background-color 0.2s ease-out;
}

.custom-volume-track:hover .custom-volume-fill {
  background-color: rgba(255, 255, 255, 0.9);
}

.custom-volume-thumb {
  position: absolute;
  left: 50%;
  width: 14px;
  height: 14px;
  background-color: #fff;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease-out;
  cursor: pointer;
  /* Enhanced subtle gradient */
  background: linear-gradient(145deg, #ffffff, #f5f5f5);
}

.custom-volume-track:hover .custom-volume-thumb {
  transform: translateX(-50%) scale(1.2);
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.5);
  filter: brightness(1.05);
}

.custom-volume-thumb.dragging {
  transform: translateX(-50%) scale(1.3) !important;
  box-shadow: 0 0 18px rgba(255, 255, 255, 0.6) !important;
  filter: brightness(1.1) !important;
}

/* Mute button styling */
.mute-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  margin-top: 5px;
}

.mute-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #fff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  border: 2px solid rgba(255, 255, 255, 0.9); /* Match progress bar */
}

.volume-slider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #fff;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.9); /* Match progress bar */
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.volume-slider:hover::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.4);
}

.volume-slider:hover::-moz-range-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.4);
}

.volume-slider:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* Active state for when user is dragging the slider */
.volume-slider:active::-webkit-slider-thumb {
  transform: scale(1.3);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.volume-slider:active::-moz-range-thumb {
  transform: scale(1.3);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

/* Play/Pause icon transition */
.icon-container {
  position: relative;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.play-icon,
.pause-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

.play-icon.visible,
.pause-icon.visible {
  opacity: 1;
  transform: scale(1);
}

.play-icon.hidden,
.pause-icon.hidden {
  opacity: 0;
  transform: scale(0.8);
}

/* LRC Player Styles */
.lrc-player {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
}

.lrc-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 25px; /* Increased padding for better readability */
  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  /* Add padding at the top and bottom for better scrolling experience */
  padding-top: 25px;
  padding-bottom: 45px;
}

.lrc-container::-webkit-scrollbar {
  width: 4px; /* Thinner scrollbar */
}

.lrc-container::-webkit-scrollbar-track {
  background: transparent;
}

.lrc-container::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2); /* More subtle scrollbar */
  border-radius: 2px;
  transition: background-color 0.3s ease;
}

.lrc-container:hover::-webkit-scrollbar-thumb {
  background-color: rgba(
    255,
    255,
    255,
    0.3
  ); /* Slightly more visible on hover */
}

.lrc-line {
  font-size: 1.2rem;
  line-height: 1.8; /* Comfortable line height for reading */
  margin-bottom: 18px;
  text-align: center;
  color: rgba(
    255,
    255,
    255,
    0.8
  ); /* Slightly reduced opacity for inactive lines */
  transition: all 0.5s ease-in-out;
  padding: 8px 0;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4); /* Subtle shadow for better contrast */
  font-weight: 500;
  position: relative;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.lrc-active-line {
  font-size: 1.4rem;
  font-weight: 700; /* Bolder for active line */
  color: #ffffff; /* Pure white for active line */
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5); /* Stronger shadow for active line */
  transition: all 0.4s ease-in-out;
  position: relative;
  letter-spacing: 0.01em; /* Subtle letter spacing for emphasis */
}

.lyrics-loading,
.lyrics-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .content-layout {
    justify-content: center;
    padding: 15px;
    margin-top: 15px;
    gap: 25px;
  }

  .player-container,
  .lyrics-panel {
    flex-basis: 45%;
  }
}

@media (max-width: 900px) {
  .content-layout {
    flex-direction: column;
    height: auto;
    max-height: none;
    padding: 15px;
    margin-top: 10px;
    gap: 20px;
    align-items: center;
  }

  .player-container {
    min-width: unset;
    width: 100%;
    max-width: 600px;
    padding: 20px;
  }

  .lyrics-panel {
    height: 300px;
    max-height: 300px;
    width: 100%;
    max-width: 600px;
  }

  .lyrics-toggle-button {
    display: block; /* Show on mobile */
    margin-top: 15px;
    padding: 8px 20px;
    font-size: 0.95rem;
  }

  .playlist-container {
    margin-top: 15px;
    padding: 15px;
    max-width: 600px;
  }
}

@media (max-width: 768px) {
  .playback-controls {
    margin: 15px 0;
    gap: 10px;
  }

  .control-button {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .play-pause-button {
    width: 55px;
    height: 55px;
    font-size: 18px;
  }

  .album-art {
    max-width: 200px;
  }

  .track-progress {
    margin: 10px 0;
  }

  .App {
    padding-bottom: 20px;
  }

  .volume-slider {
    max-width: 80px;
  }

  .track-info-container {
    padding: 0 5px;
  }

  .track-info {
    max-width: 80%;
  }
}

@media (max-width: 600px) {
  .player-container,
  .lyrics-panel,
  .playlist-container {
    padding: 15px;
    border-radius: 12px;
    max-width: 100%;
  }

  .album-art {
    max-width: 160px;
  }

  .track-title {
    font-size: 1.3rem;
  }

  .artist-name {
    font-size: 0.9rem;
  }

  .lrc-line {
    font-size: 1.1rem;
    margin-bottom: 14px;
    padding: 6px 0;
  }

  .lrc-active-line {
    font-size: 1.25rem;
  }

  .lyrics-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
  }

  .content-layout {
    gap: 15px;
    padding: 10px;
    margin-top: 5px;
  }

  body {
    font-size: 14px;
  }

  .volume-slider {
    max-width: 60px;
  }

  .heart-icon {
    font-size: 1.1rem;
  }

  .like-button {
    width: 32px;
    height: 32px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .content-layout {
    padding: 8px;
    margin-top: 5px;
  }

  .player-container,
  .lyrics-panel,
  .playlist-container {
    padding: 12px;
    border-radius: 10px;
  }

  .album-art {
    max-width: 140px;
  }

  .track-title {
    font-size: 1.2rem;
  }

  .artist-name {
    font-size: 0.85rem;
  }

  .control-button {
    width: 36px;
    height: 36px;
  }

  .play-pause-button {
    width: 50px;
    height: 50px;
  }

  .time {
    font-size: 11px;
    min-width: 35px;
  }

  .progress-bar {
    height: 6px;
  }

  .lyrics-panel {
    height: 250px;
    max-height: 250px;
  }

  .lyrics-toggle-button {
    padding: 6px 15px;
    font-size: 0.85rem;
  }

  .App {
    padding-bottom: 10px;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .player-container,
  .lyrics-panel,
  .playlist-container {
    padding: 10px;
  }

  .album-art {
    max-width: 120px;
  }

  .lyrics-panel {
    height: 200px;
  }

  .lrc-line {
    font-size: 1rem;
    margin-bottom: 12px;
    padding: 4px 0;
  }

  .lrc-active-line {
    font-size: 1.15rem;
  }
}
