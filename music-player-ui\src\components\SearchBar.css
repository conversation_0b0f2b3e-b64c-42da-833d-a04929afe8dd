/* SearchBar styling */
.search-container {
  position: sticky;
  top: 15px;
  width: 100%;
  max-width: 800px;
  margin: 15px auto 0;
  z-index: 100;
  padding: 0 20px;
}

.search-bar {
  display: flex;
  width: 100%;
  background-color: rgba(20, 20, 20, 0.85);
  backdrop-filter: blur(15px) saturate(180%);
  border-radius: 50px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}

.search-bar:focus-within {
  box-shadow: 0 12px 42px 0 rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-bar input {
  flex: 1;
  padding: 15px 20px;
  background: transparent;
  border: none;
  outline: none;
  color: #fff;
  font-size: 1rem;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
}

.search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-bar button {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border: none;
  padding: 0 25px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.95rem;
  font-family: "SF Pro", "SF Pro Display", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, sans-serif;
}

.search-bar button:hover:not(:disabled) {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
}

.search-bar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Search icon */
.search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px 0 20px;
  color: rgba(255, 255, 255, 0.7);
}

/* Floating dropdown styles */
.search-dropdown {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  right: 0;
  margin: 0 20px;
  z-index: 101;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  opacity: 0;
  pointer-events: none;
  border-radius: 15px;
}

.search-dropdown.active {
  max-height: 500px;
  opacity: 1;
  pointer-events: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .search-container {
    padding: 0 15px;
    max-width: 100%;
  }
  
  .search-bar {
    border-radius: 30px;
  }
  
  .search-bar input {
    padding: 12px 15px;
    font-size: 0.95rem;
  }
  
  .search-bar button {
    padding: 0 20px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .search-container {
    padding: 0 10px;
  }
  
  .search-bar {
    border-radius: 25px;
  }
  
  .search-bar input {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
  
  .search-bar button {
    padding: 0 15px;
    font-size: 0.85rem;
  }
}
