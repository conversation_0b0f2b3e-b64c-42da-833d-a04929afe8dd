.source-toggle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  justify-content: center;
  gap: 10px;
}

.source-label {
  font-size: 14px;
  color: #aaa;
  margin-right: 5px;
}

.source-buttons {
  display: flex;
  gap: 5px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 3px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.source-button {
  background-color: transparent;
  border: none;
  color: #aaa;
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.source-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.source-button.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.source-button.active:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
