/* Loading and Error Message Styles */

.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  margin: 20px auto;
  max-width: 400px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-message p {
  color: white;
  font-size: 16px;
  margin: 0;
}

.loading-player {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  margin: 20px auto;
  max-width: 600px;
  background-color: rgba(255, 59, 48, 0.2);
  border-left: 4px solid rgba(255, 59, 48, 0.8);
  border-radius: 6px;
  color: white;
  position: relative;
}

.error-message p {
  margin: 0;
  flex: 1;
}

.error-close-button {
  background: transparent;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0 0 0 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-close-button:hover {
  color: rgba(255, 255, 255, 0.8);
}
